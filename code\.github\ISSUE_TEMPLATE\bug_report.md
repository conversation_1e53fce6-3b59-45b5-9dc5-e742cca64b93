---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

#### Expected Behavior
<!--- Required. Describe the behavior you expect to see for your case. -->

#### Actual Behavior
<!--- Required. Describe the actual behavior for your case. -->

#### Potential Solution
<!--- Optional. Describe any potential solutions and/or thoughts as to what may be causing the difference between expected and actual behavior. -->

#### Reproducing the Problem
<!--- Required for <PERSON>. Describe how to reproduce the problem. This can be via a failing unit test or a simplified algorithm that reliably demonstrates this issue.  -->

#### System Information
<!--- Required for <PERSON>. Include any system specific information, such as OS. -->

#### Checklist
<!--- Confirm that you've provided all the required information. -->
<!--- Required fields --->
- [ ] I have completely filled out this template
- [ ] I have confirmed that this issue exists on the current `master` branch
- [ ] I have confirmed that this is not a duplicate issue by searching [issues](https://github.com/QuantConnect/Lean/issues)
<!--- Required for <PERSON>, feature request can delete the line below. -->
- [ ] I have provided detailed steps to reproduce the issue

<!--- Template inspired by https://github.com/stevemao/github-issue-templates -->

/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

using System;
using System.Collections.Generic;
using System.Linq;
using QuantConnect.Data.UniverseSelection;
using QuantConnect.Interfaces;
using QuantConnect.Securities;

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// Regression algorithm testing the behavior of the algorithm when a security is removed and re-added.
    /// It asserts that the securities are marked as non-tradable when removed and that they are tradable when re-added.
    /// It also asserts that the algorithm receives the correct security changed events for the added and removed securities.
    ///
    /// Additionally, it tests that the security is initialized after every addition, and no more.
    ///
    /// This specific algorithm tests this behavior for securities selected, deselected and re-selected from universes.
    /// </summary>
    public class SecurityInitializationOnReAdditionForUniverseSelectionRegressionAlgorithm : QCAlgorithm, IRegressionAlgorithmDefinition
    {
        private List<Symbol> _symbolsToSelect;
        private List<Symbol> _selectedSymbols;
        private int _selectionsCount;

        private Dictionary<Security, int> _securityInializationCounts = new();

        public override void Initialize()
        {
            SetStartDate(2014, 03, 24);
            SetEndDate(2014, 04, 07);
            SetCash(100000);

            UniverseSettings.Resolution = Resolution.Daily;

            var seeder = new FuncSecuritySeeder((security) =>
            {
                if (!_securityInializationCounts.TryGetValue(security, out var count))
                {
                    count = 0;
                }
                _securityInializationCounts[security] = count + 1;

                Debug($"[{Time}] Seeding {security.Symbol}");
                return GetLastKnownPrices(security);
            });

            SetSecurityInitializer(security => seeder.SeedSecurity(security));

            _symbolsToSelect = new List<Symbol>()
            {
                QuantConnect.Symbol.Create("SPY", SecurityType.Equity, Market.USA),
                QuantConnect.Symbol.Create("IWM", SecurityType.Equity, Market.USA),
                QuantConnect.Symbol.Create("QQQ", SecurityType.Equity, Market.USA),
                QuantConnect.Symbol.Create("AIG", SecurityType.Equity, Market.USA),
                QuantConnect.Symbol.Create("BAC", SecurityType.Equity, Market.USA),
                QuantConnect.Symbol.Create("IBM", SecurityType.Equity, Market.USA),
            };

            AddUniverse("MyUniverse", Resolution.Daily, SelectionFunction);
        }

        private IEnumerable<string> SelectionFunction(DateTime dateTime)
        {
            _securityInializationCounts.Clear();
            _selectionsCount++;

            _selectedSymbols = _symbolsToSelect.Skip(dateTime.Day % 2 == 0 ? 0 : 3).Take(3).ToList();
            return _selectedSymbols.Select(x => x.Value);
        }

        public override void OnSecuritiesChanged(SecurityChanges changes)
        {
            foreach (var security in changes.AddedSecurities)
            {
                if (!security.IsTradable)
                {
                    throw new RegressionTestException($"Expected the security to be tradable. Symbol: {security.Symbol}");
                }
            }

            foreach (var security in changes.RemovedSecurities)
            {
                if (security.IsTradable)
                {
                    throw new RegressionTestException($"Expected the security to be not tradable. Symbol: {security.Symbol}");
                }
            }

            if (changes.AddedSecurities.Count != _selectedSymbols.Count ||
                changes.AddedSecurities.Any(x => !_selectedSymbols.Contains(x.Symbol)))
            {
                throw new RegressionTestException($"Expected the added securities to be the selected ones. " +
                    $"Added: {string.Join(", ", changes.AddedSecurities.Select(x => x.Symbol.Value))}, " +
                    $"Selected: {string.Join(", ", _selectedSymbols)}");
            }

            if (changes.AddedSecurities.Count != _securityInializationCounts.Count ||
                changes.AddedSecurities.Any(x => !_securityInializationCounts.TryGetValue(x, out var count) || count != 1))
            {
                throw new RegressionTestException($"Expected all contracts to be initialized. " +
                    $"Added: {string.Join(", ", changes.AddedSecurities.Select(x => x.Symbol.Value))}, " +
                    $"Initialized: {string.Join(", ", _securityInializationCounts.Select(x => $"{x.Key.Symbol.Value} - {x.Value}"))}");
            }

            if (changes.RemovedSecurities.Count > 0)
            {
                var expectedDeselectedSymbols = _symbolsToSelect.Where(x => !_selectedSymbols.Contains(x)).ToList();

                if (changes.RemovedSecurities.Count != expectedDeselectedSymbols.Count ||
                    changes.RemovedSecurities.Any(x => !expectedDeselectedSymbols.Contains(x.Symbol)))
                {
                    throw new RegressionTestException($"Expected the removed securities to be the deselected ones. " +
                        $"Removed: {string.Join(", ", changes.RemovedSecurities.Select(x => x.Symbol.Value))}, " +
                        $"Deselected: {string.Join(", ", expectedDeselectedSymbols)}");
                }
            }
        }

        public override void OnEndOfAlgorithm()
        {
            if (_selectionsCount < 2)
            {
                throw new RegressionTestException("Expected at least two selections");
            }
        }

        /// <summary>
        /// This is used by the regression test system to indicate if the open source Lean repository has the required data to run this algorithm.
        /// </summary>
        public bool CanRunLocally { get; } = true;

        /// <summary>
        /// This is used by the regression test system to indicate which languages this algorithm is written in.
        /// </summary>
        public List<Language> Languages { get; } = new() { Language.CSharp };

        /// <summary>
        /// Data Points count of all timeslices of algorithm
        /// </summary>
        public long DataPoints => 128;

        /// <summary>
        /// Data Points count of the algorithm history
        /// </summary>
        public int AlgorithmHistoryDataPoints => 150;

        /// <summary>
        /// Final status of the algorithm
        /// </summary>
        public AlgorithmStatus AlgorithmStatus => AlgorithmStatus.Completed;

        /// <summary>
        /// This is used by the regression test system to indicate what the expected statistics are from running the algorithm
        /// </summary>
        public Dictionary<string, string> ExpectedStatistics => new Dictionary<string, string>
        {
            {"Total Orders", "0"},
            {"Average Win", "0%"},
            {"Average Loss", "0%"},
            {"Compounding Annual Return", "0%"},
            {"Drawdown", "0%"},
            {"Expectancy", "0"},
            {"Start Equity", "100000"},
            {"End Equity", "100000"},
            {"Net Profit", "0%"},
            {"Sharpe Ratio", "0"},
            {"Sortino Ratio", "0"},
            {"Probabilistic Sharpe Ratio", "0%"},
            {"Loss Rate", "0%"},
            {"Win Rate", "0%"},
            {"Profit-Loss Ratio", "0"},
            {"Alpha", "0"},
            {"Beta", "0"},
            {"Annual Standard Deviation", "0"},
            {"Annual Variance", "0"},
            {"Information Ratio", "0.97"},
            {"Tracking Error", "0.097"},
            {"Treynor Ratio", "0"},
            {"Total Fees", "$0.00"},
            {"Estimated Strategy Capacity", "$0"},
            {"Lowest Capacity Asset", ""},
            {"Portfolio Turnover", "0%"},
            {"OrderListHash", "d41d8cd98f00b204e9800998ecf8427e"}
        };
    }
}

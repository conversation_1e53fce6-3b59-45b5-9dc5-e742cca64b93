name: API Tests

on:
  push:
    branches: ['*']
    tags: ['*']
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-24.04
    # Only run on push events (not on pull_request) for security reasons in order to be able to use secrets
    if: ${{ github.event_name == 'push' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Liberate disk space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: true
          large-packages: false
          docker-images: false
          swap-storage: false
      - name: Run API Tests
        uses: addnab/docker-run-action@v3
        with:
          image: quantconnect/lean:foundation
          options: --workdir /__w/Lean/Lean -v /home/<USER>/work:/__w -e GITHUB_REF=${{ github.ref }} -e QC_JOB_USER_ID=${{ secrets.QC_JOB_USER_ID }} -e QC_API_ACCESS_TOKEN=${{ secrets.QC_API_ACCESS_TOKEN }} -e QC_JOB_ORGANIZATION_ID=${{ secrets.QC_JOB_ORGANIZATION_ID }}
          shell: bash
          run: |
            # Build
            dotnet build /p:Configuration=Release /v:quiet /p:WarningLevel=1 QuantConnect.Lean.sln
            # Run Projects tests
            dotnet test ./Tests/bin/Release/QuantConnect.Tests.dll --blame-hang-timeout 7minutes --blame-crash --logger "console;verbosity=detailed" --filter "FullyQualifiedName=QuantConnect.Tests.API.ProjectTests|FullyQualifiedName=QuantConnect.Tests.API.ObjectStoreTests" -- TestRunParameters.Parameter\(name=\"log-handler\", value=\"ConsoleErrorLogHandler\"\)

name: Research Regression Tests

on:
  push:
    branches: ['*']
    tags: ['*']
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Liberate disk space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: true
          large-packages: false
          docker-images: false
          swap-storage: false

      - uses: addnab/docker-run-action@v3
        with:
          image: quantconnect/lean:foundation
          options: --workdir /__w/Lean/Lean -v /home/<USER>/work:/__w
          shell: bash
          run: |
            # install dependencies
            pip3 install papermill==2.4.0 clr-loader==0.1.6
            # install kernel
            dotnet tool install --global Microsoft.dotnet-interactive --version 1.0.607001
            # Add dotnet tools to Path
            export PATH="$HOME/.dotnet/tools:$PATH"
            # activate kernel for jupyter
            dotnet interactive jupyter install
            # Build
            dotnet build /p:Configuration=Release /v:quiet /p:WarningLevel=1 QuantConnect.Lean.sln
            # Run Tests
            dotnet test ./Tests/bin/Release/QuantConnect.Tests.dll --filter TestCategory=ResearchRegressionTests -- TestRunParameters.Parameter\(name=\"log-handler\", value=\"ConsoleErrorLogHandler\"\) TestRunParameters.Parameter\(name=\"reduced-disk-size\", value=\"true\"\)

---
name: Feature request
about: Suggest an idea for this project
title: ''
labels: ''
assignees: ''

---

#### Expected Behavior
<!--- Required. Describe the behavior you expect to see for your case. -->

#### Actual Behavior
<!--- Required. Describe the actual behavior for your case. -->

#### Potential Solution
<!--- Optional. Describe any potential solutions and/or thoughts as to what may be causing the difference between expected and actual behavior. -->

#### Checklist
<!--- Confirm that you've provided all the required information. -->
<!--- Required fields --->
- [ ] I have completely filled out this template
- [ ] I have confirmed that this issue exists on the current `master` branch
- [ ] I have confirmed that this is not a duplicate issue by searching [issues](https://github.com/QuantConnect/Lean/issues)

<!--- Template inspired by https://github.com/stevemao/github-issue-templates -->
